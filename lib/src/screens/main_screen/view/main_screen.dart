import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_screen.dart';
import 'package:opti_tickets/src/screens/services/view/services_screen.dart';
import 'package:opti_tickets/src/screens/settings/integrated_settings_screen.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);

    return Scaffold(
      bottomNavigationBar: const BottomNavBarWidget(),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: _getSelectedScreen(selectedIndex),
      ),
    );
  }

  Widget _getSelectedScreen(int index) {
    switch (index) {
      case 0:
        return const HomeScreen();
      case 1:
        return const IntegratedSettingsScreen();
      case 2:
        return const ServicesScreen();
      case 3:
        return const ServicesScreen();
      default:
        return const HomeScreen();
    }
  }
}
