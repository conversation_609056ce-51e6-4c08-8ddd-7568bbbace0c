import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/providers/client_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/models/product_model.dart';
import '../../../core/shared/providers/product_providers.dart';
import '../models/quotation_model.dart';
import '../providers/quotation_providers.dart';

class AddQuotationStepsScreen extends HookConsumerWidget {
  const AddQuotationStepsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentStep = useState(0);
    final selectedClient = useState<Client?>(null);
    final selectedProducts = useState<List<Product>>([]);
    final selectedProductDetails = useState<List<SelectedProductDetail>>([]);
    final searchController = useTextEditingController();
    final searchQuery = useState('');
    final userLimitControllers = useState<Map<int, TextEditingController>>({});

    const totalSteps = 3;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        title: Text(context.tr.saveQuotation),
        backgroundColor: ColorManager.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        ),
      ),
      body: Column(
        children: [
          // Step Indicator
          _buildStepIndicator(context, currentStep.value, totalSteps),

          // Step Content
          Expanded(
            child: _buildStepContent(
              context,
              ref,
              currentStep.value,
              selectedClient,
              selectedProducts,
              selectedProductDetails,
              searchController,
              searchQuery,
              userLimitControllers,
            ),
          ),

          // Navigation Buttons
          _buildNavigationButtons(
            context,
            ref,
            currentStep,
            totalSteps,
            selectedClient,
            selectedProducts,
            selectedProductDetails,
            userLimitControllers,
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(
      BuildContext context, int currentStep, int totalSteps) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        children: List.generate(totalSteps, (index) {
          final isActive = index <= currentStep;
          final isCompleted = index < currentStep;

          return Expanded(
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isActive
                        ? ColorManager.primaryColor
                        : ColorManager.lightGrey,
                  ),
                  child: Center(
                    child: isCompleted
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : Text(
                            '${index + 1}',
                            style: AppTextStyles.labelLarge.copyWith(
                              color: isActive
                                  ? Colors.white
                                  : ColorManager.darkGrey,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                if (index < totalSteps - 1)
                  Expanded(
                    child: Container(
                      height: 2,
                      margin: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.padding8),
                      color: isCompleted
                          ? ColorManager.primaryColor
                          : ColorManager.lightGrey,
                    ),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildStepContent(
    BuildContext context,
    WidgetRef ref,
    int currentStep,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    switch (currentStep) {
      case 0:
        return _buildClientSelectionStep(
            context, ref, selectedClient, searchController, searchQuery);
      case 1:
        return _buildProductSelectionStep(context, ref, selectedClient,
            selectedProducts, userLimitControllers);
      case 2:
        return _buildSubProductSelectionStep(
            context, ref, selectedProducts, selectedProductDetails);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildClientSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Client?> selectedClient,
    TextEditingController searchController,
    ValueNotifier<String> searchQuery,
  ) {
    final clientsAsyncValue = ref.watch(getClientListFutureProvider);

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 1 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.clientSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,

          // Search Field
          TextField(
            controller: searchController,
            onChanged: (value) {
              searchQuery.value = value;
            },
            decoration: InputDecoration(
              hintText: '${context.tr.search} ${context.tr.clients}',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: searchQuery.value.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        searchController.clear();
                        searchQuery.value = '';
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppRadius.radius12),
              ),
            ),
          ),
          AppGaps.gap16,
          clientsAsyncValue.when(
            data: (clientModel) {
              final allClients = clientModel.clients;

              // Filter clients based on search query
              final clients = allClients.where((client) {
                if (searchQuery.value.isEmpty) return true;

                final query = searchQuery.value.toLowerCase();
                return client.clientname.toLowerCase().contains(query) ||
                    client.clientstatus.toLowerCase().contains(query);
              }).toList();

              if (clients.isEmpty) {
                return Center(
                  child: Column(
                    children: [
                      const Icon(
                        Icons.people_outline,
                        size: 64,
                        color: ColorManager.lightGrey,
                      ),
                      AppGaps.gap16,
                      Text(
                        context.tr.noDataFound,
                        style: AppTextStyles.body.copyWith(
                          color: ColorManager.lightGrey,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Expanded(
                child: ListView.separated(
                  itemCount: clients.length,
                  separatorBuilder: (context, index) => AppGaps.gap12,
                  itemBuilder: (context, index) {
                    final client = clients[index];
                    final isSelected =
                        selectedClient.value?.clientID == client.clientID;

                    return Card(
                      elevation: isSelected ? 4 : 1,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                        side: BorderSide(
                          color: isSelected
                              ? ColorManager.primaryColor
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: ListTile(
                        onTap: () {
                          selectedClient.value = client;
                        },
                        leading: CircleAvatar(
                          backgroundColor: isSelected
                              ? ColorManager.primaryColor
                              : ColorManager.lightGrey,
                          child: Icon(
                            Icons.business,
                            color: isSelected
                                ? Colors.white
                                : ColorManager.darkGrey,
                          ),
                        ),
                        title: Text(
                          client.clientname,
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          client.clientstatus,
                          style: AppTextStyles.labelLarge.copyWith(
                            color: ColorManager.darkGrey,
                          ),
                        ),
                        trailing: isSelected
                            ? const Icon(
                                Icons.check_circle,
                                color: ColorManager.primaryColor,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorManager.errorColor,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.somethingWentWrong,
                    style: AppTextStyles.subTitle,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    final productsAsyncValue = ref.watch(getProductsListByClientFutureProvider(
        selectedClient.value?.clientID ?? 0));

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 2 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.productSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,
          Text(
            context.tr.selectParentProduct,
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap16,
          Expanded(
            child: productsAsyncValue.when(
              data: (productListModel) {
                if (productListModel.data.categoryLists.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.inventory_2_outlined,
                          size: 64,
                          color: ColorManager.lightGrey,
                        ),
                        AppGaps.gap16,
                        Text(
                          context.tr.noProductsAvailable,
                          style: AppTextStyles.body.copyWith(
                            color: ColorManager.lightGrey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                final products = productListModel.data.categoryLists;

                return ListView.separated(
                  itemCount: products.length,
                  separatorBuilder: (context, index) => AppGaps.gap12,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    final isSelected = selectedProducts.value
                        .any((p) => p.productId == product.productId);

                    // Ensure controller exists for this product
                    if (!userLimitControllers.value
                        .containsKey(product.productId)) {
                      userLimitControllers.value[product.productId] =
                          TextEditingController();
                    }

                    final controller =
                        userLimitControllers.value[product.productId]!;
                    final hasUserLimit = product.hasLimitUser == 1;

                    return GestureDetector(
                      onTap: () {
                        final updatedProducts =
                            List<Product>.from(selectedProducts.value);

                        if (!isSelected) {
                          if (!updatedProducts
                              .any((p) => p.productId == product.productId)) {
                            updatedProducts.add(product);
                          }
                        } else {
                          updatedProducts.removeWhere(
                              (p) => p.productId == product.productId);
                          // Clear user limit when deselected
                          controller.clear();
                        }

                        selectedProducts.value = updatedProducts;
                      },
                      child: Card(
                        elevation: isSelected ? 4 : 1,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                          side: BorderSide(
                            color: isSelected
                                ? ColorManager.primaryColor
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(AppSpaces.padding16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: isSelected
                                        ? ColorManager.primaryColor
                                        : ColorManager.lightGrey,
                                    child: Icon(
                                      Icons.inventory_2,
                                      color: isSelected
                                          ? Colors.white
                                          : ColorManager.grey,
                                    ),
                                  ),
                                  AppGaps.gap12,
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          product.productName,
                                          style:
                                              AppTextStyles.subTitle.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        AppGaps.gap4,
                                        Text(
                                          product.hasSubList == 1
                                              ? '${product.subProducts.length} ${context.tr.selectSubProducts}'
                                              : context.tr.done,
                                          style:
                                              AppTextStyles.labelLarge.copyWith(
                                            color: ColorManager.darkGrey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Checkbox(
                                    value: isSelected,
                                    onChanged: (value) {
                                      final updatedProducts =
                                          List<Product>.from(
                                              selectedProducts.value);

                                      if (value == true) {
                                        if (!updatedProducts.any((p) =>
                                            p.productId == product.productId)) {
                                          updatedProducts.add(product);
                                        }
                                      } else {
                                        updatedProducts.removeWhere((p) =>
                                            p.productId == product.productId);
                                        // Clear user limit when deselected
                                        controller.clear();
                                      }

                                      selectedProducts.value = updatedProducts;
                                    },
                                  ),
                                ],
                              ),

                              // User limit input field (only show if product has user limit and is selected)
                              if (hasUserLimit && isSelected) ...[
                                AppGaps.gap12,
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        controller: controller,
                                        keyboardType: TextInputType.number,
                                        decoration: InputDecoration(
                                          labelText: context.tr.userLimit,
                                          hintText: context.tr.enterUserLimit,
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                                AppRadius.radius8),
                                          ),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                            horizontal: AppSpaces.padding12,
                                            vertical: AppSpaces.padding8,
                                          ),
                                        ),
                                      ),
                                    ),
                                    AppGaps.gap8,
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: AppSpaces.padding8,
                                        vertical: AppSpaces.padding4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: ColorManager.primaryColor
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(
                                            AppRadius.radius4),
                                      ),
                                      child: Text(
                                        context.tr.users,
                                        style:
                                            AppTextStyles.labelSmall.copyWith(
                                          color: ColorManager.primaryColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: ColorManager.errorColor,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.failedToLoadProducts,
                      style: AppTextStyles.subTitle,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubProductSelectionStep(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
  ) {
    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${context.tr.step} 3 ${context.tr.of1} 3',
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap8,
          Text(
            context.tr.subProductSelection,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,
          Text(
            context.tr.selectSubProducts,
            style: AppTextStyles.labelLarge.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.gap16,
          Expanded(
            child: selectedProducts.value.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.inventory_outlined,
                          size: 64,
                          color: ColorManager.lightGrey,
                        ),
                        AppGaps.gap16,
                        Text(
                          context.tr.pleaseSelectAtLeastOneProduct,
                          style: AppTextStyles.body.copyWith(
                            color: ColorManager.lightGrey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    itemCount: selectedProducts.value.length,
                    separatorBuilder: (context, index) => AppGaps.gap16,
                    itemBuilder: (context, index) {
                      final product = selectedProducts.value[index];

                      // Find existing product detail or create new one
                      final existingDetail = selectedProductDetails.value
                          .where((detail) =>
                              detail.product.productId == product.productId)
                          .firstOrNull;

                      final selectedSubProducts =
                          existingDetail?.selectedSubProducts ?? <SubProduct>[];

                      return Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(AppSpaces.padding16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.productName,
                                style: AppTextStyles.title.copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              AppGaps.gap12,
                              if (product.hasSubList == 1 &&
                                  product.subProducts.isNotEmpty)
                                ...product.subProducts.map((subProduct) {
                                  final isSelected = selectedSubProducts.any(
                                      (sub) =>
                                          sub.subProductId ==
                                          subProduct.subProductId);

                                  return CheckboxListTile(
                                    dense: true,
                                    contentPadding: EdgeInsets.zero,
                                    title: Text(
                                      subProduct.subProductName,
                                      style: AppTextStyles.body,
                                    ),
                                    subtitle: Text(
                                      '${context.tr.cost}: ${subProduct.subProductCost}',
                                      style: AppTextStyles.labelSmall.copyWith(
                                        color: ColorManager.darkGrey,
                                      ),
                                    ),
                                    value: isSelected,
                                    onChanged: (value) {
                                      _updateSubProductSelection(
                                        selectedProductDetails,
                                        product,
                                        subProduct,
                                        value ?? false,
                                      );
                                    },
                                  );
                                }).toList()
                              else
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: AppSpaces.padding8),
                                  child: Text(
                                    '${context.tr.done} - ${context.tr.noProductsAvailable}',
                                    style: AppTextStyles.labelLarge.copyWith(
                                      color: ColorManager.darkGrey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  void _updateSubProductSelection(
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    Product product,
    SubProduct subProduct,
    bool isSelected,
  ) {
    final updatedDetails =
        List<SelectedProductDetail>.from(selectedProductDetails.value);

    // Find existing detail for this product
    final existingIndex = updatedDetails.indexWhere(
      (detail) => detail.product.productId == product.productId,
    );

    if (existingIndex != -1) {
      // Update existing detail
      final existingDetail = updatedDetails[existingIndex];
      final updatedSubProducts =
          List<SubProduct>.from(existingDetail.selectedSubProducts);

      if (isSelected) {
        if (!updatedSubProducts
            .any((sub) => sub.subProductId == subProduct.subProductId)) {
          updatedSubProducts.add(subProduct);
        }
      } else {
        updatedSubProducts
            .removeWhere((sub) => sub.subProductId == subProduct.subProductId);
      }

      updatedDetails[existingIndex] = existingDetail.copyWith(
        selectedSubProducts: updatedSubProducts,
      );
    } else {
      // Create new detail
      if (isSelected) {
        updatedDetails.add(SelectedProductDetail(
          product: product,
          selectedSubProducts: [subProduct],
          userLimit: 0,
        ));
      }
    }

    selectedProductDetails.value = updatedDetails;
  }

  Widget _buildNavigationButtons(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<int> currentStep,
    int totalSteps,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      color: Colors.white,
      child: Row(
        children: [
          if (currentStep.value > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  currentStep.value--;
                },
                child: Text(context.tr.previous),
              ),
            ),
          if (currentStep.value > 0) AppGaps.gap16,
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (currentStep.value < totalSteps - 1) {
                  // Validate current step
                  if (_canProceedToNextStep(currentStep.value, selectedClient,
                      selectedProducts, selectedProductDetails)) {
                    currentStep.value++;
                  } else {
                    // Show validation message
                    _showValidationMessage(context, currentStep.value);
                  }
                } else {
                  // Final step - save quotation
                  _saveQuotation(context, ref, selectedClient, selectedProducts,
                      selectedProductDetails, userLimitControllers);
                }
              },
              child: Text(
                currentStep.value < totalSteps - 1
                    ? context.tr.next
                    : context.tr.finish,
                style: AppTextStyles.whiteLabelLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceedToNextStep(
    int currentStep,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
  ) {
    switch (currentStep) {
      case 0:
        return selectedClient.value != null;
      case 1:
        return selectedProducts.value.isNotEmpty;
      case 2:
        // Check if products without sub-products are selected, or if sub-products are selected for products that have them
        return selectedProducts.value.every((product) {
          if (product.hasSubList != 1 || product.subProducts.isEmpty) {
            return true; // Products without sub-products are automatically valid
          }

          // For products with sub-products, check if at least one sub-product is selected
          final productDetail = selectedProductDetails.value
              .where((detail) => detail.product.productId == product.productId)
              .firstOrNull;

          return productDetail != null &&
              productDetail.selectedSubProducts.isNotEmpty;
        });
      default:
        return false;
    }
  }

  void _showValidationMessage(BuildContext context, int currentStep) {
    String message;
    switch (currentStep) {
      case 0:
        message = context.tr.pleaseSelectClientFirst;
        break;
      case 1:
        message = context.tr.pleaseSelectAtLeastOneProduct;
        break;
      case 2:
        message = context.tr.pleaseSelectAtLeastOneSubProduct;
        break;
      default:
        message = context.tr.somethingWentWrong;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorManager.errorColor,
      ),
    );
  }

  Future<void> _saveQuotation(
    BuildContext context,
    WidgetRef ref,
    ValueNotifier<Client?> selectedClient,
    ValueNotifier<List<Product>> selectedProducts,
    ValueNotifier<List<SelectedProductDetail>> selectedProductDetails,
    ValueNotifier<Map<int, TextEditingController>> userLimitControllers,
  ) async {
    if (selectedClient.value == null) {
      _showValidationMessage(context, 0);
      return;
    }

    try {
      // Prepare quotation details
      final List<QuotationDetail> qDetails = [];

      for (final product in selectedProducts.value) {
        // Get user limit from controller
        final userLimitText =
            userLimitControllers.value[product.productId]?.text ?? '';
        final userLimit = int.tryParse(userLimitText) ?? 0;

        if (product.hasSubList != 1 || product.subProducts.isEmpty) {
          // Product without sub-products
          qDetails.add(QuotationDetail(
            productId: product.productId.toString(),
            subProductId: product.productId
                .toString(), // Use product ID as sub-product ID
            hasLimitUsers: userLimit,
          ));
        } else {
          // Product with sub-products
          final productDetail = selectedProductDetails.value
              .where((detail) => detail.product.productId == product.productId)
              .firstOrNull;

          if (productDetail != null) {
            for (final subProduct in productDetail.selectedSubProducts) {
              qDetails.add(QuotationDetail(
                productId: product.productId.toString(),
                subProductId: subProduct.subProductId.toString(),
                hasLimitUsers: userLimit,
              ));
            }
          }
        }
      }

      if (qDetails.isEmpty) {
        _showValidationMessage(context, 1);
        return;
      }

      // Record current time
      final now = DateTime.now();
      final startDate = DateFormat('yyyy-MM-dd', 'en').format(now);
      final startTime = DateFormat('HH:mm', 'en').format(now);

      // Calculate finish time (1 hour later)
      final finishTime = now.add(const Duration(hours: 1));
      final finishDate = DateFormat('yyyy-MM-dd', 'en').format(finishTime);
      final finishTimeString = DateFormat('HH:mm', 'en').format(finishTime);

      final quotationData = AddQuotationModel(
        qInfo: QuotationInfo(
          clientID: selectedClient.value!.clientID.toString(),
          createdDate: startDate,
        ),
        qDetails: qDetails,
        task: QuotationTask(
          startDate: startDate,
          startTime: startTime,
          finishDate: finishDate,
          finishTime: finishTimeString,
        ),
      );

      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Save quotation
      final quotationController = ref.read(quotationControllerProvider);
      await quotationController.addQuotation(quotationData: quotationData);

      // Hide loading
      Navigator.pop(context);

      // Close the add quotation screen
      Navigator.pop(context);

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr.quotationSaved),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Hide loading if showing
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.tr.somethingWentWrong),
          backgroundColor: ColorManager.errorColor,
        ),
      );
    }
  }
}
