// import 'package:flutter/material.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:intl/intl.dart';
// import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
// import 'package:opti_tickets/src/core/theme/color_manager.dart';
// import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
// import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// import '../../../core/shared/models/product_model.dart';
// import '../../../core/shared/widgets/fields/base_search_sheet.dart';
// import '../../../core/shared/widgets/fields/product_multi_select_sheet.dart';
// import '../../../core/shared/widgets/loading/loading_widget.dart';
// import '../../clients/models/client_model.dart';
// import '../../clients/providers/client_providers.dart';
// import '../../clients/view/add_client_screen.dart';
//
// class AddQuotationScreen extends ConsumerStatefulWidget {
//   const AddQuotationScreen({super.key});
//
//   @override
//   ConsumerState<AddQuotationScreen> createState() => _AddQuotationScreenState();
// }
//
// class _AddQuotationScreenState extends ConsumerState<AddQuotationScreen> {
//   final _formKey = GlobalKey<FormState>();
//
//   late DateTime _startTime;
//   late String _startDate;
//   late String _startTimeString;
//
//   // Selected client and products
//   Client? _selectedClient;
//   List<SelectedProductDetail> _selectedProducts = [];
//
//   @override
//   void initState() {
//     super.initState();
//
//     // Record start time
//     _startTime = DateTime.now();
//     _startDate = DateFormat('yyyy-MM-dd', 'en').format(_startTime);
//     _startTimeString = DateFormat('HH:mm', 'en').format(_startTime);
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//   }
//
//   Future<void> _submitQuotation() async {
//     if (!_formKey.currentState!.validate()) return;
//
//     // Validate that a client is selected
//     if (_selectedClient == null) {
//       context.showBarMessage(context.tr.pleaseSelectClientFirst, isError: true);
//       return;
//     }
//
//     try {
//       // Validate that at least one sub-product is selected
//       final hasSelectedSubProducts = _selectedProducts.any(
//           (selectedProduct) => selectedProduct.selectedSubProducts.isNotEmpty);
//
//       if (!hasSelectedSubProducts) {
//         context.showBarMessage(context.tr.pleaseSelectAtLeastOneSubProduct,
//             isError: true);
//         return;
//       }
//
//       // Record finish time
//       final finishTime = DateTime.now();
//       final finishDate = DateFormat('yyyy-MM-dd', 'en').format(finishTime);
//       final finishTimeString = DateFormat('HH:mm', 'en').format(finishTime);
//
//       // Convert selected products to quotation details
//       // Only send sub-products, each with its parent product ID
//       final List<QuotationDetail> qDetails = [];
//       for (final selectedProduct in _selectedProducts) {
//         if (selectedProduct.product.hasSubProducts &&
//             selectedProduct.selectedSubProducts.isNotEmpty) {
//           // Add each selected sub-product with its parent product ID
//           for (final subProduct in selectedProduct.selectedSubProducts) {
//             qDetails.add(QuotationDetail(
//               productId: selectedProduct.product.productId, // Parent product ID
//               subProductId: subProduct.subProductId, // Sub-product ID
//               hasLimitUsers: selectedProduct.hasLimitUsers,
//             ));
//           }
//         }
//         // Note: We don't send parent products without sub-products
//         // Only sub-products are sent to the API
//       }
//
//       final quotationData = AddQuotationModel(
//         qInfo: QuotationInfo(
//           clientID: _selectedClient!.clientID.toString(),
//           createdDate: _startDate,
//         ),
//         qDetails: qDetails,
//         task: QuotationTask(
//           startDate: _startDate,
//           startTime: _startTimeString,
//           finishDate: finishDate,
//           finishTime: finishTimeString,
//         ),
//       );
//
//       final quotationController = ref.read(quotationControllerProvider);
//
//       await quotationController.addQuotation(quotationData: quotationData);
//
//       ref.invalidate(getQuotationListFutureProvider);
//       Navigator.pop(context);
//       context.showBarMessage(context.tr.quotationSaved);
//     } catch (e) {
//       context.showBarMessage(context.tr.somethingWentWrong, isError: true);
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: ColorManager.lightGreyBackground,
//       appBar: AppBar(
//         backgroundColor: Colors.transparent,
//         title: Text(
//           context.tr.saveQuotation,
//           style: AppTextStyles.title,
//         ),
//         centerTitle: true,
//         leading: IconButton(
//           onPressed: () => Navigator.pop(context),
//           icon: const Icon(Icons.arrow_back_ios_new),
//         ),
//       ),
//       body: Form(
//         key: _formKey,
//         child: SingleChildScrollView(
//           padding: const EdgeInsets.all(AppSpaces.padding16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Header
//               // Container(
//               //   width: double.infinity,
//               //   padding: const EdgeInsets.all(AppSpaces.padding16),
//               //   decoration: BoxDecoration(
//               //     color: ColorManager.primaryColor.withOpacity(0.1),
//               //     borderRadius: BorderRadius.circular(AppRadius.radius12),
//               //   ),
//               //   child: Column(
//               //     children: [
//               //       const Icon(
//               //         Icons.description,
//               //         size: 48,
//               //         color: ColorManager.primaryColor,
//               //       ),
//               //       AppGaps.gap8,
//               //       Text(
//               //         context.tr.saveQuotation,
//               //         style: AppTextStyles.labelMedium.copyWith(
//               //           color: ColorManager.primaryColor,
//               //           fontWeight: FontWeight.w600,
//               //         ),
//               //       ),
//               //     ],
//               //   ),
//               // ),
//               // AppGaps.gap24,
//
//               // Client Selection
//               Consumer(
//                 builder: (context, ref, child) {
//                   final clientsAsyncValue =
//                       ref.watch(getClientListFutureProvider);
//
//                   return clientsAsyncValue.when(
//                     data: (clientListModel) {
//                       final allClients = clientListModel.clients;
//
//                       return Column(
//                         crossAxisAlignment: CrossAxisAlignment.end,
//                         children: [
//                           // text button add client
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                             children: [
//                               Text(
//                                 context.tr.client,
//                                 style: AppTextStyles.labelMedium.copyWith(
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                               TextButton(
//                                 onPressed: () {
//                                   Navigator.push(
//                                     context,
//                                     MaterialPageRoute(
//                                       builder: (context) =>
//                                           const AddClientScreen(),
//                                     ),
//                                   ).then((_) {
//                                     // Refresh the list when returning from add screen
//                                     ref.invalidate(getClientListFutureProvider);
//                                   });
//                                 },
//                                 child: Row(
//                                   mainAxisAlignment: MainAxisAlignment.end,
//                                   mainAxisSize: MainAxisSize.min,
//                                   children: [
//                                     const Icon(
//                                       Icons.add,
//                                       color: ColorManager.primaryColor,
//                                     ),
//                                     AppGaps.gap4,
//                                     Text(
//                                       context.tr.addClient,
//                                       style: AppTextStyles.labelMedium.copyWith(
//                                         color: ColorManager.primaryColor,
//                                         fontWeight: FontWeight.w600,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                           BaseSearchSheet(
//                             selectedValue: _selectedClient,
//                             label: context.tr.selectClient,
//                             data: allClients,
//                             onChanged: (selectedClient) {
//                               setState(() {
//                                 _selectedClient = selectedClient;
//                                 // Clear selected products when client changes
//                                 _selectedProducts = [];
//                               });
//                             },
//                             itemModelAsName: (client) => client.clientname,
//                           ),
//                         ],
//                       );
//                     },
//                     loading: () => Container(
//                       padding: const EdgeInsets.all(AppSpaces.padding16),
//                       decoration: BoxDecoration(
//                         border: Border.all(color: ColorManager.lightGrey),
//                         borderRadius: BorderRadius.circular(AppRadius.radius12),
//                       ),
//                       child: Row(
//                         children: [
//                           const Icon(Icons.business),
//                           AppGaps.gap12,
//                           Text(
//                             context.tr.loadingClients,
//                             style: AppTextStyles.labelMedium.copyWith(
//                               color: ColorManager.darkGrey,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     error: (error, stackTrace) => Container(
//                       padding: const EdgeInsets.all(AppSpaces.padding16),
//                       decoration: BoxDecoration(
//                         border: Border.all(color: ColorManager.errorColor),
//                         borderRadius: BorderRadius.circular(AppRadius.radius12),
//                       ),
//                       child: Row(
//                         children: [
//                           const Icon(Icons.error,
//                               color: ColorManager.errorColor),
//                           AppGaps.gap12,
//                           Text(
//                             context.tr.failedToLoadClients,
//                             style: AppTextStyles.labelMedium.copyWith(
//                               color: ColorManager.errorColor,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   );
//                 },
//               ),
//               AppGaps.gap16,
//
//               // Product Multi-Selection
//               ProductMultiSelectSheet(
//                 selectedProducts: _selectedProducts,
//                 onChanged: (selectedProducts) {
//                   setState(() {
//                     _selectedProducts = selectedProducts;
//                   });
//                 },
//                 label: context.tr.selectProduct,
//                 clientId: _selectedClient?.clientID,
//               ),
//               AppGaps.gap48,
//
//               // Submit Button
//               Button(
//                 isLoading:
//                     ref.watch(quotationControllerNotifierProvider).isLoading,
//                 loadingWidget: const LoadingWidget(),
//                 onPressed: _submitQuotation,
//                 label: context.tr.submit,
//                 color: ColorManager.primaryColor,
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
