import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/attendance_card_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/contracts_expiration_slider.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/home_app_bar_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/widgets/stats_card_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeFuture = ref.watch(getHomeFutureProvider);

    final homeModel = homeFuture.when(
      data: (homeData) => homeData,
      loading: () => const HomeModel(),
      error: (error, stackTrace) => const HomeModel(),
    );

    final isLoading = homeFuture.isLoading;
    final homeData = isLoading ? demoHomeModel : homeModel;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            automaticallyImplyLeading: false,
            pinned: true,
            expandedHeight: 110.h,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(AppRadius.radius24),
                bottomRight: Radius.circular(AppRadius.radius24),
              ),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Skeletonizer(
                enabled: isLoading,
                child: HomeAppBarWidget(
                  employee: homeData.employee,
                ),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: AttendanceCardWidget(
                attendance: homeData.attendance,
                workTimings: homeData.employee.workTimings,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: ContractsExpirationSlider(
                contracts: homeData.contractsAboutExpire,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: StatsCardWidget(
                stats: homeData.currentMonthStats,
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
          SliverToBoxAdapter(
            child: Skeletonizer(
              enabled: isLoading,
              child: _buildHomeUpdatesSection(context, homeData),
            ),
          ),
          const SliverToBoxAdapter(
            child: AppGaps.gap16,
          ),
        ],
      ),
    );
  }

  Widget _buildHomeUpdatesSection(BuildContext context, HomeModel homeData) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr.homeUpdates,
            style: AppTextStyles.title.copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          AppGaps.gap16,

          // Tasks and Tickets Row
          Row(
            children: [
              Expanded(
                child: _buildUpdateCard(
                  context,
                  title: context.tr.activeTasks,
                  value: homeData.tasks.active.toString(),
                  subtitle: context.tr.finishedTasks,
                  subtitleValue: homeData.tasks.finished.toString(),
                  color: ColorManager.primaryColor,
                  icon: Icons.task_alt,
                ),
              ),
              AppGaps.gap12,
              Expanded(
                child: _buildUpdateCard(
                  context,
                  title: context.tr.activeTickets,
                  value: homeData.tickets.active.toString(),
                  subtitle: context.tr.finishedTickets,
                  subtitleValue: homeData.tickets.finished.toString(),
                  color: Colors.orange,
                  icon: Icons.confirmation_number,
                ),
              ),
            ],
          ),

          AppGaps.gap16,

          // Contracts Row
          Row(
            children: [
              Expanded(
                child: _buildUpdateCard(
                  context,
                  title: context.tr.nearToExpire,
                  value: homeData.contracts.nearExpire.toString(),
                  subtitle: context.tr.hasExpired,
                  subtitleValue: homeData.contracts.isExpired.toString(),
                  color: Colors.red,
                  icon: Icons.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateCard(
    BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
    required String subtitleValue,
    required Color color,
    required IconData icon,
  }) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius16),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.radius16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.1),
              color.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpaces.padding8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppRadius.radius8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            AppGaps.gap8,
            Row(
              children: [
                Text(
                  title,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const Spacer(),
                Text(
                  value,
                  style: AppTextStyles.title.copyWith(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            AppGaps.gap8,
            Row(
              children: [
                Text(
                  subtitle,
                  style: AppTextStyles.labelSmall.copyWith(
                    color: ColorManager.darkGrey,
                  ),
                ),
                const Spacer(),
                Text(
                  subtitleValue,
                  style: AppTextStyles.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ColorManager.darkGrey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
